'use client';

import { useState, useEffect, useCallback, useRef } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { extractBookInformation } from '../utils/embeddingAnalysis';
import { ArrowLeft } from 'lucide-react';

export default function AlternateScenarioPage() {
  const router = useRouter();
  const [isGenerating, setIsGenerating] = useState(true);
  const [gameData, setGameData] = useState(null);
  const [error, setError] = useState('');
  const [userPrompt, setUserPrompt] = useState('');
  const [bookInfo, setBookInfo] = useState({
    bookTitle: '',
    author: '',
    changeLocation: '',
    originalEvent: ''
  });

  // Check if we're in the browser
  const [isClient, setIsClient] = useState(false);
  useEffect(() => {
    setIsClient(true);
  }, []);

  // Canvas-related state and refs
  const canvasRef = useRef(null);
  const containerRef = useRef(null);
  const [gameLoaded, setGameLoaded] = useState(false);
  const [loadingProgress, setLoadingProgress] = useState(0);
  const [gameError, setGameError] = useState(null);
  const engineRef = useRef(null);

  // Function to generate the scenario for the game
  const generateScenario = useCallback(async (bookData, whatIfPrompt) => {
    try {
      // Ensure we have the required data
      if (!bookData.bookTitle || !bookData.author || !whatIfPrompt) {
        setError('Missing required information to generate scenario.');
        setIsGenerating(false);
        return;
      }

      // Generate the game data using the new API endpoint
      const response = await fetch('/api/alternate-scenario-game-data', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          bookTitle: bookData.bookTitle,
          author: bookData.author,
          changeLocation: bookData.changeLocation,
          whatIfPrompt: whatIfPrompt
        }),
      });

      const result = await response.json();

      if (response.ok && result.questions) {
        setGameData(result);
        // Store the game data in localStorage for the Godot game to access
        localStorage.setItem('storyGameData', JSON.stringify(result));
      } else {
        setError(result.error || 'Failed to generate story data');
      }
    } catch (error) {
      console.error('Error generating scenario:', error);
      setError(error.message || 'An unexpected error occurred');
    } finally {
      setIsGenerating(false);
    }
  }, []);

  // Load data from localStorage and generate scenario automatically
  useEffect(() => {
    if (!isClient) return;

    async function loadDataAndGenerateScenario() {
      try {
        // Load analysis results and user prompt from localStorage
        const savedResults = localStorage.getItem('analysisResults');
        const savedPrompt = localStorage.getItem('userPrompt');

        if (!savedResults || !savedPrompt) {
          setError('No analysis results or prompt found. Please analyze a PDF first.');
          setIsGenerating(false);
          return;
        }

        const parsedResults = JSON.parse(savedResults);
        setUserPrompt(savedPrompt);

        // Extract book information from analysis results
        const extractedBookInfo = extractBookInformation(parsedResults);
        setBookInfo(extractedBookInfo);

        // Generate the alternate scenario automatically
        await generateScenario(extractedBookInfo, savedPrompt);
      } catch (error) {
        console.error('Error loading data and generating scenario:', error);
        setError('Error loading data: ' + (error.message || 'Unknown error'));
        setIsGenerating(false);
      }
    }

    loadDataAndGenerateScenario();
  }, [isClient, generateScenario]);

  // Game initialization effect
  useEffect(() => {
    if (!gameData || !canvasRef.current || !containerRef.current) return;

    // Set canvas to maintain aspect ratio and fit within container
    const updateCanvasSize = () => {
      if (canvasRef.current && containerRef.current) {
        const container = containerRef.current;
        const rect = container.getBoundingClientRect();

        // Add some padding to ensure it fits comfortably
        const padding = 20;
        const availableWidth = Math.max(rect.width - padding, 300);
        const availableHeight = Math.max(rect.height - padding, 200);

        // Maintain 16:9 aspect ratio
        const aspectRatio = 16/9;

        let width = availableWidth;
        let height = width / aspectRatio;

        // If height is too large, constrain by height instead
        if (height > availableHeight) {
          height = availableHeight;
          width = height * aspectRatio;
        }

        // Ensure minimum viable size
        const minWidth = 400;
        const minHeight = 225; // 400/16*9

        if (width < minWidth) {
          width = Math.min(minWidth, availableWidth);
          height = width / aspectRatio;
        }

        if (height < minHeight) {
          height = Math.min(minHeight, availableHeight);
          width = height * aspectRatio;
        }

        // Final bounds check
        width = Math.min(width, availableWidth);
        height = Math.min(height, availableHeight);

        // Set the internal resolution (this affects game rendering quality)
        canvasRef.current.width = 1920; // Fixed internal resolution
        canvasRef.current.height = 1080; // Fixed internal resolution

        // Set the display size
        canvasRef.current.style.width = `${Math.floor(width)}px`;
        canvasRef.current.style.height = `${Math.floor(height)}px`;
        canvasRef.current.style.maxWidth = '100%';
        canvasRef.current.style.maxHeight = '100%';
        canvasRef.current.style.objectFit = 'contain';

        console.log(`Canvas sized to: ${Math.floor(width)}x${Math.floor(height)} (container: ${rect.width}x${rect.height})`);
      }
    };

    updateCanvasSize();
    window.addEventListener('resize', updateCanvasSize);

    let engine = null;

    // ✅ Define Module FIRST — critical
    window.Module = {
      locateFile: (path) => `/${path}`
    };

    const initializeGame = () => {
      const GODOT_CONFIG = {
        executable: 'index',
        canvasResizePolicy: 0, // 0 = None - we handle sizing ourselves
        fileSizes: {
          'index.pck': 97008,
          'index.wasm': 49282035
        },
        focusCanvas: true
      };

      engine = new window.Engine(GODOT_CONFIG);
      engineRef.current = engine;

      engine.startGame({
        canvas: canvasRef.current,
        onProgress: (current, total) => {
          if (total > 0) {
            setLoadingProgress((current / total) * 100);
          }
        }
      }).then(() => {
        setGameLoaded(true);
        // Ensure proper sizing after game loads
        setTimeout(updateCanvasSize, 100);
      }).catch(err => {
        setGameError('Failed to start game: ' + err.message);
      });
    };

    // Since the script is loaded via layout, we can directly initialize
    if (window.Engine) {
      initializeGame();
    } else {
      // Fallback: wait for script to load
      const checkEngine = setInterval(() => {
        if (window.Engine) {
          clearInterval(checkEngine);
          initializeGame();
        }
      }, 100);

      // Cleanup interval after 10 seconds
      setTimeout(() => {
        clearInterval(checkEngine);
        if (!window.Engine) {
          setGameError('Failed to load game engine.');
        }
      }, 10000);
    }

    return () => {
      if (engineRef.current?.requestQuit) engineRef.current.requestQuit();
      window.removeEventListener('resize', updateCanvasSize);
    };
  }, [gameData]);

  const handleLogout = async () => {
    try {
      // Redirect to main page (authentication removed)
      router.push('/');
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  if (!isClient) {
    return null;
  }

  return (
    <div className="min-h-screen bg-black flex flex-col overflow-hidden">
      {/* Header */}
      <div className="bg-gray-900 p-4 flex items-center justify-between flex-shrink-0">
        <Link
          href="/"
          className="flex items-center text-white hover:text-gray-300 transition-colors"
        >
          <ArrowLeft className="w-5 h-5 mr-2" />
          Back to The Money Tales
        </Link>
        <h1 className="text-white text-xl font-bold">Alternate Scenario Game</h1>
        <button
          onClick={handleLogout}
          className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
        >
          Logout
        </button>
      </div>

      {/* Loading State - Full Screen */}
      {isGenerating && (
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center text-white">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"></div>
            <p className="text-lg">Generating your interactive story...</p>
            <p className="text-sm text-gray-400 mt-2">This may take a moment as we create your personalized story</p>
          </div>
        </div>
      )}

      {/* Error State - Full Screen */}
      {error && (
        <div className="flex-1 flex items-center justify-center">
          <div className="bg-red-900/50 border border-red-700 p-6 rounded-lg text-center max-w-md">
            <h2 className="text-xl font-bold mb-2 text-white">Error</h2>
            <p className="mb-4 text-red-200">{error}</p>
            <Link
              href="/"
              className="px-6 py-3 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
            >
              Return to PDF Analysis
            </Link>
          </div>
        </div>
      )}

      {/* Game Container */}
      {!isGenerating && !error && gameData && (
        <div className="flex-1 flex flex-col overflow-hidden min-h-0">
          <div ref={containerRef} className="flex-1 relative flex items-center justify-center bg-gray-900 min-h-0 w-full">
            {/* Loading Screen */}
            {!gameLoaded && !gameError && (
              <div className="absolute inset-0 flex flex-col items-center justify-center z-10">
                <div className="text-white text-xl mb-4">Loading Game...</div>
                <div className="w-64 h-2 bg-gray-600 rounded-full overflow-hidden">
                  <div
                    className="h-full bg-blue-500 transition-all duration-300"
                    style={{ width: `${loadingProgress}%` }}
                  />
                </div>
                <div className="text-gray-300 mt-2">{Math.round(loadingProgress)}%</div>
              </div>
            )}

            {/* Error Screen */}
            {gameError && (
              <div className="absolute inset-0 flex flex-col items-center justify-center z-10">
                <div className="text-white text-xl mb-4">Error Loading Game</div>
                <div className="text-red-200 text-center max-w-md">
                  {gameError}
                </div>
                <button
                  onClick={() => window.location.reload()}
                  className="mt-4 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors"
                >
                  Retry
                </button>
              </div>
            )}

            {/* Game Canvas */}
            <canvas
              ref={canvasRef}
              id="canvas"
              className="block"
              style={{
                imageRendering: 'pixelated',
                imageRendering: '-moz-crisp-edges',
                imageRendering: 'crisp-edges',
              }}
            >
              HTML5 canvas appears to be unsupported in the current browser.
              Please try updating or use a different browser.
            </canvas>
          </div>

         
        </div>
      )}
    </div>
  );
}