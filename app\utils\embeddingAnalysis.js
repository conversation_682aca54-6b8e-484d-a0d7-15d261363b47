'use client';

import { pipeline } from '@xenova/transformers';

// Cache for the pipelines
let embeddingPipeline = null;
let sentimentPipeline = null;

/**
 * Initialize the embedding pipeline
 * @returns {Promise<Object>} The embedding pipeline
 */
export async function getEmbeddingPipeline() {
  if (embeddingPipeline) {
    return embeddingPipeline;
  }

  try {
    // Load the embedding model - using a sentence-transformers model for better semantic similarity
    embeddingPipeline = await pipeline(
      'feature-extraction',
      'Xenova/all-MiniLM-L6-v2' // A good balance between performance and quality
    );

    console.log('Embedding pipeline loaded successfully');
    return embeddingPipeline;
  } catch (error) {
    console.error('Error loading embedding pipeline:', error);
    throw error;
  }
}

/**
 * Initialize the sentiment analysis pipeline (lazy loading)
 * @returns {Promise<Object>} The sentiment analysis pipeline
 */
export async function getSentimentPipeline() {
  if (sentimentPipeline) {
    return sentimentPipeline;
  }

  try {
    sentimentPipeline = await pipeline(
      'sentiment-analysis',
      'Xenova/distilbert-base-uncased-finetuned-sst-2-english'
    );
    console.log('Sentiment analysis pipeline loaded successfully');
    return sentimentPipeline;
  } catch (error) {
    console.error('Error loading sentiment analysis pipeline:', error);
    // Return a fallback function that returns neutral sentiment
    return {
      predict: () => [{ label: 'NEUTRAL', score: 0.5 }]
    };
  }
}

/**
 * Generate embeddings for a text string
 * @param {string} text - The text to embed
 * @returns {Promise<Float32Array>} The embedding vector
 */
export async function generateEmbedding(text) {
  const pipeline = await getEmbeddingPipeline();

  try {
    // Generate embeddings
    const result = await pipeline(text, {
      pooling: 'mean', // Use mean pooling to get a fixed-size vector
      normalize: true, // Normalize the embeddings for better cosine similarity
    });

    // Return the embedding vector
    return result.data;
  } catch (error) {
    console.error('Error generating embedding:', error);
    throw error;
  }
}

/**
 * Generate embeddings for a collection of text chunks
 * @param {Array<Object>} chunks - Array of chunk objects with at least an id and content field
 * @returns {Promise<Array<Object>>} The chunks with added embeddings
 */
export async function generateEmbeddingsForChunks(chunks, onProgress = null) {
  console.log(`Generating embeddings for ${chunks.length} chunks`);

  const chunksWithEmbeddings = [];

  for (let i = 0; i < chunks.length; i++) {
    const chunk = chunks[i];

    try {
      // Skip chunks without content
      if (!chunk.content) {
        console.warn(`Chunk ${chunk.id} has no content, skipping embedding generation`);
        chunksWithEmbeddings.push(chunk);
        continue;
      }

      console.log(`Generating embedding for chunk: ${chunk.id} (${i + 1}/${chunks.length})`);

      // Call progress callback if provided
      if (onProgress) {
        onProgress(i + 1, chunks.length, chunk.id);
      }

      // Generate embedding for the chunk content
      const embedding = await generateEmbedding(chunk.content);

      // Add the embedding to the chunk
      chunksWithEmbeddings.push({
        ...chunk,
        embedding
      });

      // Add a small delay to prevent blocking the UI
      await new Promise(resolve => setTimeout(resolve, 10));

    } catch (error) {
      console.error(`Error generating embedding for chunk ${chunk.id}:`, error);
      // Add the chunk without embedding
      chunksWithEmbeddings.push(chunk);
    }
  }

  return chunksWithEmbeddings;
}

/**
 * Calculate cosine similarity between two vectors
 * @param {Float32Array} vec1 - First vector
 * @param {Float32Array} vec2 - Second vector
 * @returns {number} Cosine similarity score (between -1 and 1)
 */
export function cosineSimilarity(vec1, vec2) {
  if (!vec1 || !vec2 || vec1.length !== vec2.length) {
    return 0;
  }

  let dotProduct = 0;
  let mag1 = 0;
  let mag2 = 0;

  for (let i = 0; i < vec1.length; i++) {
    dotProduct += vec1[i] * vec2[i];
    mag1 += vec1[i] * vec1[i];
    mag2 += vec2[i] * vec2[i];
  }

  mag1 = Math.sqrt(mag1);
  mag2 = Math.sqrt(mag2);

  if (mag1 === 0 || mag2 === 0) {
    return 0;
  }

  return dotProduct / (mag1 * mag2);
}

/**
 * Calculate word match score between query and text
 * @param {string} query - User query
 * @param {string} text - Text to match against
 * @returns {number} Word match score (between 0 and 1)
 */
export function calculateWordMatchScore(query, text) {
  if (!query || !text) {
    return 0;
  }

  // Normalize and tokenize
  const queryWords = query.toLowerCase().split(/\W+/).filter(word => word.length > 2);
  const textWords = text.toLowerCase().split(/\W+/).filter(word => word.length > 2);

  if (queryWords.length === 0) {
    return 0;
  }

  // Count matching words
  let matchCount = 0;
  for (const queryWord of queryWords) {
    if (textWords.includes(queryWord)) {
      matchCount++;
    }
  }

  return matchCount / queryWords.length;
}

/**
 * Analyze sentiment of a text
 * @param {string} text - Text to analyze
 * @returns {Promise<Object>} Sentiment analysis result
 */
export async function analyzeSentiment(text) {
  const pipeline = await getSentimentPipeline();

  try {
    const result = await pipeline(text);
    return result[0]; // Return the top sentiment
  } catch (error) {
    console.error('Error analyzing sentiment:', error);
    return { label: 'NEUTRAL', score: 0.5 };
  }
}

/**
 * Calculate sentiment match score between query and text
 * @param {Object} querySentiment - Sentiment of the query
 * @param {Object} textSentiment - Sentiment of the text
 * @returns {number} Sentiment match score (between 0 and 1)
 */
export function calculateSentimentMatchScore(querySentiment, textSentiment) {
  // If sentiments are the same, return 1
  if (querySentiment.label === textSentiment.label) {
    return 1;
  }

  // If sentiments are opposite (POSITIVE vs NEGATIVE), return 0
  if (
    (querySentiment.label === 'POSITIVE' && textSentiment.label === 'NEGATIVE') ||
    (querySentiment.label === 'NEGATIVE' && textSentiment.label === 'POSITIVE')
  ) {
    return 0;
  }

  // If one is neutral, return a middle score based on the confidence of the other
  return 0.5;
}

/**
 * Rank chunks based on relevance to a query
 * @param {string} query - User query
 * @param {Array<Object>} chunks - Array of chunks with embeddings
 * @returns {Promise<Array<Object>>} Ranked chunks with scores
 */
export async function rankChunksByRelevance(query, chunks) {
  // Generate embedding for the query
  const queryEmbedding = await generateEmbedding(query);

  // Analyze sentiment of the query
  const querySentiment = await analyzeSentiment(query);

  // Calculate scores for each chunk
  const scoredChunks = await Promise.all(
    chunks.map(async (chunk) => {
      // Skip chunks without content or embeddings
      if (!chunk.content || !chunk.embedding) {
        return {
          ...chunk,
          score: 0,
          details: {
            similarityScore: 0,
            wordMatchScore: 0,
            sentimentScore: 0,
            querySentiment: querySentiment || { label: 'neutral', score: 0 },
            chunkSentiment: { label: 'neutral', score: 0 }
          }
        };
      }

      // Calculate semantic similarity score (50% weight)
      const similarityScore = cosineSimilarity(queryEmbedding, chunk.embedding);

      // Calculate word match score (30% weight)
      const wordMatchScore = calculateWordMatchScore(query, chunk.content);

      // Calculate sentiment match score (20% weight)
      const chunkSentiment = await analyzeSentiment(chunk.content);
      const sentimentScore = calculateSentimentMatchScore(querySentiment, chunkSentiment);

      // Calculate weighted score
      const weightedScore = (
        similarityScore * 0.5 +
        wordMatchScore * 0.3 +
        sentimentScore * 0.2
      );

      return {
        ...chunk,
        score: weightedScore,
        details: {
          similarityScore,
          wordMatchScore,
          sentimentScore,
          querySentiment,
          chunkSentiment
        }
      };
    })
  );

  // Sort by score (descending)
  return scoredChunks.sort((a, b) => b.score - a.score);
}

/**
 * Extract book information from analysis results
 * @param {Array<Object>} analysisResults - The ranked chunks with scores
 * @returns {Object} Extracted book information
 */
export function extractBookInformation(analysisResults) {
  if (!analysisResults || analysisResults.length === 0) {
    return {
      bookTitle: 'Unknown Book',
      author: 'Unknown Author',
      changeLocation: 'Unknown Section',
      originalEvent: ''
    };
  }

  // Get the top-ranked chunk for the original event
  const topChunk = analysisResults[0];

  // Extract metadata if available
  const metadata = topChunk.metadata || {};

  // Try to extract book title from the content if not in metadata
  let bookTitle = metadata.title || 'Unknown Book';
  let author = metadata.author || 'Unknown Author';

  // Look for title and author patterns in the content
  if (topChunk.content) {
    // Try to extract title if not already found
    if (bookTitle === 'Unknown Book') {
      // Look for common title patterns
      const titleMatch = topChunk.content.match(/(?:title|book):\s*["']?([^"'\n]+)["']?/i) ||
                         topChunk.content.match(/["']([^"'\n]{5,50})["']\s+by\s+/i);
      if (titleMatch && titleMatch[1]) {
        bookTitle = titleMatch[1].trim();
      }
    }

    // Try to extract author if not already found
    if (author === 'Unknown Author') {
      // Look for common author patterns
      const authorMatch = topChunk.content.match(/(?:author|by|written by):\s*["']?([^"'\n]+)["']?/i) ||
                          topChunk.content.match(/by\s+([A-Z][a-z]+\s+[A-Z][a-z]+)/);
      if (authorMatch && authorMatch[1]) {
        author = authorMatch[1].trim();
      }
    }
  }

  // Get chapter/section information
  let changeLocation = metadata.section || '';

  // If we have path information, use it for the section
  if (topChunk.path && Array.isArray(topChunk.path) && topChunk.path.length > 0) {
    changeLocation = topChunk.path.join(' > ');
  } else if (topChunk.title) {
    // Use the chunk title as the section if available
    changeLocation = topChunk.title;
  } else if (topChunk.startPage && topChunk.endPage) {
    // Use page range as a fallback
    changeLocation = `Pages ${topChunk.startPage}-${topChunk.endPage}`;
  }

  // For the original event, use the content of the top chunk
  // but limit it to a reasonable length
  const originalEvent = topChunk.content
    ? (topChunk.content.length > 1000 ? topChunk.content.substring(0, 1000) + '...' : topChunk.content)
    : '';

  return {
    bookTitle,
    author,
    changeLocation,
    originalEvent
  };
}
