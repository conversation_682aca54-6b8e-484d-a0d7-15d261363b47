import { TABLES, isDevelopment } from './config';
import crypto from 'crypto';

// Simple in-memory storage for demo purposes
// In a real application, you would use a proper database
let waitlistData = [];

export const checkEmailExists = async (email) => {
    const existingUser = waitlistData.find(user => user.email === email.toLowerCase());

    if (!existingUser) {
      return { exists: false };
    }

    return {
      exists: true,
      status: existingUser.status || 'active',
      docId: existingUser.id
    };
};

export const reactivateUser = async (docId) => {
    const userIndex = waitlistData.findIndex(user => user.id === docId);
    const newPosition = await getWaitlistPosition();

    if (userIndex !== -1) {
        waitlistData[userIndex] = {
            ...waitlistData[userIndex],
            status: 'active',
            reactivatedAt: new Date().toISOString(),
            position: newPosition,
            effectivePosition: newPosition,
            positionBonus: 0,
            totalReferrals: 0
        };
    }

    return newPosition;
};

// Generate a unique, URL-friendly referral code
const generateReferralCode = (email) => {
    const timestamp = Date.now().toString();
    const uniqueString = `${email}${timestamp}${process.env.REFERRAL_SECRET}`;
    return crypto
        .createHash('md5')
        .update(uniqueString)
        .digest('hex')
        .substring(0, 8);
};

// Get current position in waitlist
export const getWaitlistPosition = async () => {
    const activeUsers = waitlistData.filter(user => user.status === 'active');
    return activeUsers.length + 1;
};

// Process referral and update referrer's position
export const processReferral = async (referralCode) => {
    if (!referralCode) return null;

    const referrerIndex = waitlistData.findIndex(user =>
        user.referralCode === referralCode && user.status === 'active'
    );

    if (referrerIndex !== -1) {
        const referrer = waitlistData[referrerIndex];
        waitlistData[referrerIndex] = {
            ...referrer,
            positionBonus: (referrer.positionBonus || 0) + 5,
            totalReferrals: (referrer.totalReferrals || 0) + 1
        };

        return {
            referrerId: referrer.id,
            referrerEmail: referrer.email
        };
    }

    return null;
};

// Add new user to waitlist
export const addToWaitlist = async (email, referralCode = null) => {
    const newReferralCode = generateReferralCode(email);
    const referrerInfo = await processReferral(referralCode);

    const position = await getWaitlistPosition();
    const newId = crypto.randomUUID();

    const docData = {
        id: newId,
        email: email.toLowerCase(),
        timestamp: new Date().toISOString(),
        status: 'active',
        referralCode: newReferralCode,
        position: position,
        positionBonus: 0,
        effectivePosition: position,
        referredBy: referrerInfo?.referrerId || null,
        referredByEmail: referrerInfo?.referrerEmail || null,
        totalReferrals: 0,
        environment: isDevelopment() ? 'development' : 'production'
    };

    waitlistData.push(docData);
    return docData;
};

export const getEffectivePosition = async (position, positionBonus) => {
    return Math.max(1, position - positionBonus);
};