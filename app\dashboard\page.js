'use client';

import { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { useRouter } from 'next/navigation';

export default function DashboardPage() {
  const router = useRouter();
  const [isSidebarOpen, setIsSidebarOpen] = useState(true);
  const [activeSection, setActiveSection] = useState('chat');
  const [expandedMenus, setExpandedMenus] = useState({
    chatHistory: false,
    settings: false
  });

  // Mock data for courses
  const courses = [
    {
      id: 1,
      title: "Course Title in case of two lines",
      description: "A tech-savvy AI, ideal for tech support, gadget recommendations...",
      progress: 45,
      image: "https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=400&h=300&fit=crop"
    },
    {
      id: 2,
      title: "Course Title in case of two lines",
      description: "A tech-savvy AI, ideal for tech support, gadget recommendations...",
      progress: 80,
      image: "https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=400&h=300&fit=crop"
    },
    {
      id: 3,
      title: "Course Title in case of two lines",
      description: "A tech-savvy AI, ideal for tech support, gadget recommendations...",
      progress: 30,
      image: "https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=400&h=300&fit=crop"
    },
    {
      id: 4,
      title: "Course Title in case of two lines",
      description: "A tech-savvy AI, ideal for tech support, gadget recommendations...",
      progress: 65,
      image: "https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=400&h=300&fit=crop"
    },
    {
      id: 5,
      title: "Course Title in case of two lines",
      description: "A tech-savvy AI, ideal for tech support, gadget recommendations...",
      progress: 15,
      image: "https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=400&h=300&fit=crop"
    },
    {
      id: 6,
      title: "Course Title in case of two lines",
      description: "A tech-savvy AI, ideal for tech support, gadget recommendations...",
      progress: 90,
      image: "https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=400&h=300&fit=crop"
    }
  ];

  const toggleMenu = (menu) => {
    setExpandedMenus(prev => ({
      ...prev,
      [menu]: !prev[menu]
    }));
  };

  const handleContinuePlaying = (courseId) => {
    // Navigate to course or handle continuation
    console.log('Continuing course:', courseId);
    router.push(`/course/${courseId}`);
  };

  return (
    <div className="flex h-screen bg-[#2a2d32] overflow-hidden">
      {/* Sidebar */}
      <aside className={`${isSidebarOpen ? 'w-64' : 'w-16'} bg-[#1e2023] transition-all duration-300 flex flex-col`}>
        {/* Logo/Brand */}
        <div className="p-4 border-b border-[#3a3d42]">
          <Link href="/" className="flex items-center space-x-2">
            {isSidebarOpen ? (
              <h1 className="font-slackey text-2xl text-white">MoneyTales</h1>
            ) : (
              <span className="font-slackey text-xl text-white">M</span>
            )}
          </Link>
        </div>

        {/* Navigation */}
        <nav className="flex-1 p-4 space-y-2">
          {/* Chat */}
          <button
            onClick={() => setActiveSection('chat')}
            className={`w-full flex items-center space-x-3 px-3 py-2 rounded-md transition-colors ${
              activeSection === 'chat' 
                ? 'bg-[#8B5CF6] text-white' 
                : 'text-[#696F79] hover:bg-[#3a3d42] hover:text-white'
            }`}
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" 
                d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
            </svg>
            {isSidebarOpen && <span className="font-inter text-sm">Chat</span>}
          </button>

          {/* Chat History */}
          <div>
            <button
              onClick={() => toggleMenu('chatHistory')}
              className="w-full flex items-center justify-between px-3 py-2 rounded-md text-[#696F79] hover:bg-[#3a3d42] hover:text-white transition-colors"
            >
              <div className="flex items-center space-x-3">
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" 
                    d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                {isSidebarOpen && <span className="font-inter text-sm">Chat History</span>}
              </div>
              {isSidebarOpen && (
                <svg className={`w-4 h-4 transition-transform ${expandedMenus.chatHistory ? 'rotate-180' : ''}`} 
                  fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
                </svg>
              )}
            </button>
            {expandedMenus.chatHistory && isSidebarOpen && (
              <div className="mt-2 ml-8 space-y-1">
                <button className="w-full text-left px-3 py-1 text-sm text-[#696F79] hover:text-white transition-colors">
                  Yesterday
                </button>
                <button className="w-full text-left px-3 py-1 text-sm text-[#696F79] hover:text-white transition-colors">
                  Last Week
                </button>
                <button className="w-full text-left px-3 py-1 text-sm text-[#696F79] hover:text-white transition-colors">
                  Last Month
                </button>
              </div>
            )}
          </div>

          {/* AI Personalities */}
          <button
            onClick={() => setActiveSection('personalities')}
            className={`w-full flex items-center space-x-3 px-3 py-2 rounded-md transition-colors ${
              activeSection === 'personalities' 
                ? 'bg-[#8B5CF6] text-white' 
                : 'text-[#696F79] hover:bg-[#3a3d42] hover:text-white'
            }`}
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" 
                d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
            </svg>
            {isSidebarOpen && <span className="font-inter text-sm">AI Personalities</span>}
          </button>

          {/* Settings */}
          <div>
            <button
              onClick={() => toggleMenu('settings')}
              className="w-full flex items-center justify-between px-3 py-2 rounded-md text-[#696F79] hover:bg-[#3a3d42] hover:text-white transition-colors"
            >
              <div className="flex items-center space-x-3">
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" 
                    d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" 
                    d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
                {isSidebarOpen && <span className="font-inter text-sm">Settings</span>}
              </div>
              {isSidebarOpen && (
                <svg className={`w-4 h-4 transition-transform ${expandedMenus.settings ? 'rotate-180' : ''}`} 
                  fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
                </svg>
              )}
            </button>
            {expandedMenus.settings && isSidebarOpen && (
              <div className="mt-2 ml-8 space-y-1">
                <button className="w-full text-left px-3 py-1 text-sm text-[#696F79] hover:text-white transition-colors">
                  Tone
                </button>
                <button className="w-full text-left px-3 py-1 text-sm text-[#696F79] hover:text-white transition-colors">
                  Language
                </button>
                <button className="w-full text-left px-3 py-1 text-sm text-[#696F79] hover:text-white transition-colors">
                  Formality Level
                </button>
              </div>
            )}
          </div>
        </nav>

        {/* Toggle Sidebar */}
        <div className="p-4 border-t border-[#3a3d42]">
          <button
            onClick={() => setIsSidebarOpen(!isSidebarOpen)}
            className="w-full flex items-center justify-center p-2 rounded-md text-[#696F79] hover:bg-[#3a3d42] hover:text-white transition-colors"
          >
            <svg className={`w-5 h-5 transition-transform ${isSidebarOpen ? '' : 'rotate-180'}`} 
              fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" 
                d="M11 19l-7-7 7-7m8 14l-7-7 7-7" />
            </svg>
          </button>
        </div>
      </aside>

      {/* Main Content */}
      <main className="flex-1 flex flex-col overflow-hidden">
        {/* Header */}
        <header className="bg-[#1e2023] border-b border-[#3a3d42] px-6 py-4">
          <div className="flex items-center justify-between">
            <h2 className="text-2xl font-bold text-white font-inter">Dashboard</h2>
            <div className="flex items-center space-x-4">
              {/* Layout Toggle */}
              <button className="p-2 rounded-md text-[#696F79] hover:bg-[#3a3d42] hover:text-white transition-colors">
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" 
                    d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
                </svg>
              </button>
              
              {/* Shield Icon */}
              <button className="p-2 rounded-md text-[#696F79] hover:bg-[#3a3d42] hover:text-white transition-colors">
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" 
                    d="M20.618 5.984A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016zM12 9v2m0 4h.01" />
                </svg>
              </button>

              {/* User Avatar */}
              <button className="flex items-center space-x-2 p-1 rounded-full hover:bg-[#3a3d42] transition-colors">
                <div className="w-10 h-10 rounded-full bg-gradient-to-r from-[#8B5CF6] to-[#7C3AED] flex items-center justify-center">
                  <span className="text-white font-medium">U</span>
                </div>
              </button>
            </div>
          </div>
        </header>

        {/* Content Area */}
        <div className="flex-1 overflow-y-auto p-6">
          <h3 className="text-xl font-semibold text-white mb-6 font-inter">My Course</h3>
          
          {/* Course Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {courses.map((course) => (
              <div key={course.id} className="bg-[#1e2023] rounded-lg overflow-hidden hover:shadow-lg transition-shadow">
                {/* Course Image */}
                <div className="relative h-48 bg-[#3a3d42]">
                  <Image
                    src={course.image}
                    alt={course.title}
                    width={400}
                    height={192}
                    className="w-full h-full object-cover"
                    onError={(e) => {
                      e.target.style.display = 'none';
                      e.target.nextSibling.style.display = 'flex';
                    }}
                  />
                  <div className="absolute inset-0 hidden items-center justify-center bg-[#3a3d42]">
                    <svg className="w-16 h-16 text-[#696F79]" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5"/>
                    </svg>
                  </div>
                </div>

                {/* Course Content */}
                <div className="p-4 space-y-3">
                  <h4 className="font-inter font-semibold text-white text-lg leading-tight">
                    {course.title}
                  </h4>
                  <p className="text-[#696F79] text-sm leading-relaxed">
                    {course.description}
                  </p>
                  
                  {/* Progress Bar */}
                  <div className="space-y-2">
                    <div className="flex justify-between text-xs text-[#696F79]">
                      <span>Progress</span>
                      <span>{course.progress}%</span>
                    </div>
                    <div className="w-full bg-[#3a3d42] rounded-full h-2">
                      <div 
                        className="bg-[#8B5CF6] h-2 rounded-full transition-all duration-300"
                        style={{ width: `${course.progress}%` }}
                      />
                    </div>
                  </div>

                  {/* Continue Button */}
                  <button
                    onClick={() => handleContinuePlaying(course.id)}
                    className="w-full py-2.5 px-4 bg-[#8B5CF6] text-white rounded-md font-medium hover:bg-[#7C3AED] transition-colors duration-200 flex items-center justify-center space-x-2 text-sm font-inter"
                  >
                    <span>Continue Playing</span>
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" 
                        d="M13 7l5 5m0 0l-5 5m5-5H6" />
                    </svg>
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </main>
    </div>
  );
}